#!/bin/bash

# HRMS Docker Deployment Script
# Builds and deploys the HRMS application using Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  build       Build the Docker image"
    echo "  dev         Start development environment"
    echo "  prod        Start production environment"
    echo "  stop        Stop all containers"
    echo "  clean       Clean up containers and images"
    echo "  logs        Show application logs"
    echo "  health      Check application health"
    echo ""
    echo "Options:"
    echo "  --rebuild   Force rebuild of Docker image"
    echo "  --no-cache  Build without using cache"
    echo ""
    echo "Examples:"
    echo "  $0 build --no-cache"
    echo "  $0 dev"
    echo "  $0 prod --rebuild"
    echo "  $0 logs"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi

    print_success "Docker and Docker Compose are available."
}

# Build Docker image
build_image() {
    local no_cache=""
    if [[ "$*" == *"--no-cache"* ]]; then
        no_cache="--no-cache"
        print_warning "Building without cache..."
    fi

    print_status "Building HRMS Docker image..."
    docker build $no_cache -t hrms-app:latest .
    print_success "Docker image built successfully."
}

# Start development environment
start_dev() {
    local rebuild=""
    if [[ "$*" == *"--rebuild"* ]]; then
        rebuild="--build"
        print_warning "Rebuilding image for development..."
    fi

    print_status "Starting HRMS development environment..."
    docker-compose -f docker-compose.dev.yml up $rebuild -d

    print_success "Development environment started!"
    echo ""
    echo "🌐 Application URLs:"
    echo "   • Main App: http://localhost:5020"
    echo "   • API Docs: http://localhost:5020/api/swagger"
    echo ""
    echo "🔑 Test Credentials:"
    echo "   • Super Admin: <EMAIL> / Admin123!"
    echo "   • Org Admin: <EMAIL> / Admin123!"
    echo ""
    echo "📋 Useful Commands:"
    echo "   • View logs: $0 logs"
    echo "   • Stop: $0 stop"
    echo "   • Health check: $0 health"
}

# Start production environment
start_prod() {
    local rebuild=""
    if [[ "$*" == *"--rebuild"* ]]; then
        rebuild="--build"
        print_warning "Rebuilding image for production..."
    fi

    print_status "Starting HRMS production environment..."
    docker-compose up $rebuild -d

    print_success "Production environment started!"
    echo ""
    echo "🌐 Application URLs:"
    echo "   • Main App: http://localhost:5020"
    echo "   • With Nginx: http://localhost:80"
    echo ""
    echo "📋 Useful Commands:"
    echo "   • View logs: $0 logs"
    echo "   • Stop: $0 stop"
    echo "   • Health check: $0 health"
}

# Stop all containers
stop_containers() {
    print_status "Stopping HRMS containers..."
    docker-compose -f docker-compose.dev.yml down 2>/dev/null || true
    docker-compose down 2>/dev/null || true
    print_success "All containers stopped."
}

# Clean up containers and images
clean_up() {
    print_status "Cleaning up HRMS containers and images..."
    
    # Stop containers
    stop_containers
    
    # Remove containers
    docker-compose -f docker-compose.dev.yml rm -f 2>/dev/null || true
    docker-compose rm -f 2>/dev/null || true
    
    # Remove images
    docker rmi hrms-app:latest 2>/dev/null || true
    
    # Remove unused volumes (optional)
    print_warning "Removing unused Docker volumes..."
    docker volume prune -f
    
    print_success "Cleanup completed."
}

# Show application logs
show_logs() {
    print_status "Showing HRMS application logs..."
    
    if docker ps | grep -q hrms-dev; then
        docker-compose -f docker-compose.dev.yml logs -f hrms-app
    elif docker ps | grep -q hrms-application; then
        docker-compose logs -f hrms-app
    else
        print_error "No HRMS containers are running."
        exit 1
    fi
}

# Check application health
check_health() {
    print_status "Checking HRMS application health..."
    
    local container_name=""
    if docker ps | grep -q hrms-dev; then
        container_name="hrms-dev"
    elif docker ps | grep -q hrms-application; then
        container_name="hrms-application"
    else
        print_error "No HRMS containers are running."
        exit 1
    fi
    
    # Check container status
    local status=$(docker inspect --format='{{.State.Health.Status}}' $container_name 2>/dev/null || echo "no-healthcheck")
    
    if [ "$status" = "healthy" ]; then
        print_success "Application is healthy ✅"
    elif [ "$status" = "unhealthy" ]; then
        print_error "Application is unhealthy ❌"
        echo "Recent health check logs:"
        docker inspect --format='{{range .State.Health.Log}}{{.Output}}{{end}}' $container_name
    else
        print_warning "Health check not available, testing manually..."
        if curl -f http://localhost:5020/api/v1/health >/dev/null 2>&1; then
            print_success "Application is responding ✅"
        else
            print_error "Application is not responding ❌"
        fi
    fi
}

# Main execution
main() {
    if [ $# -eq 0 ]; then
        show_usage
        exit 1
    fi

    check_docker

    case "$1" in
        build)
            build_image "$@"
            ;;
        dev)
            start_dev "$@"
            ;;
        prod)
            start_prod "$@"
            ;;
        stop)
            stop_containers
            ;;
        clean)
            clean_up
            ;;
        logs)
            show_logs
            ;;
        health)
            check_health
            ;;
        *)
            print_error "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
