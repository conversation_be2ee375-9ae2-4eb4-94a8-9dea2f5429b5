# HRMS Single-Port Deployment Guide

This guide explains how to deploy both the .NET Core backend and React frontend on the same port for the HRMS system.

## 🎯 Overview

The HRMS application has been configured to run both the backend API and frontend React application on a single port (5020). This approach:

- **Simplifies deployment** - Only one port to manage
- **Eliminates CORS issues** - Frontend and API share the same origin
- **Improves security** - Reduces attack surface
- **Easier reverse proxy setup** - Single endpoint to configure

## 🏗️ Architecture

```
┌─────────────────────────────────────────┐
│           Single Port (5020)            │
├─────────────────────────────────────────┤
│  .NET Core Backend (Kestrel Server)    │
├─────────────────────────────────────────┤
│  ├─ /api/*     → API Endpoints         │
│  ├─ /api/swagger → Swagger UI (dev)    │
│  └─ /*         → React SPA             │
└─────────────────────────────────────────┘
```

## 🚀 Quick Start

### 1. Build and Test Locally

```bash
# Build both frontend and backend
./build-and-deploy.sh

# Test the single-port deployment
./test-single-port.sh
```

### 2. Access the Application

- **Main Application**: http://localhost:5020
- **API Documentation**: http://localhost:5020/api/swagger (development only)

## 📁 File Structure Changes

### Frontend Changes
- **vite.config.ts**: Updated to build to `backend/HRMS.API/wwwroot`
- **src/config/api.ts**: Environment-aware API base URL configuration
- **package.json**: Added production build scripts

### Backend Changes
- **Program.cs**: Added static file serving and SPA fallback
- **HRMS.API.csproj**: Added SpaServices.Extensions package
- **appsettings.json**: Added URL configuration

## 🔧 Configuration Details

### Environment Detection

The frontend automatically detects the environment:

```typescript
// Development: API calls go to http://localhost:5020/api/v1
// Production: API calls go to /api/v1 (same origin)
BASE_URL: import.meta.env.PROD 
  ? '/api/v1' 
  : 'http://localhost:5020/api/v1'
```

### Static File Serving

The backend serves static files in this order:
1. **API routes** (`/api/*`) → Backend controllers
2. **Static files** → React build files from `wwwroot`
3. **SPA fallback** → `index.html` for client-side routing

## 🛠️ Build Process

### Automated Build Script

The `build-and-deploy.sh` script performs:

1. **Dependency checks** - Verifies Node.js, npm, and .NET Core
2. **Clean builds** - Removes previous build artifacts
3. **Frontend build** - Builds React app to `backend/HRMS.API/wwwroot`
4. **Backend build** - Compiles .NET Core application
5. **Publish** - Creates deployment-ready package in `./publish`

### Manual Build Steps

If you prefer manual building:

```bash
# 1. Build frontend
npm run build:production

# 2. Build backend
cd backend
dotnet build --configuration Release

# 3. Publish for deployment
cd HRMS.API
dotnet publish --configuration Release --output ../../publish
```

## 🚀 Deployment Options

### Option 1: Development/Testing

```bash
cd backend/HRMS.API
dotnet run --configuration Release
```

### Option 2: Production with Kestrel

```bash
cd publish
dotnet HRMS.API.dll
```

### Option 3: IIS Deployment

1. Copy `publish` folder to IIS server
2. Create IIS application pointing to the folder
3. Ensure .NET Core hosting bundle is installed

### Option 4: Docker Deployment

```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app
COPY publish/ .
EXPOSE 5020
ENTRYPOINT ["dotnet", "HRMS.API.dll"]
```

## 🔒 Security Considerations

### CORS Configuration

CORS is still configured for development flexibility:
- Development: Allows localhost origins
- Production: Should be updated with actual domain

### HTTPS Configuration

For production, ensure HTTPS is properly configured:

```json
{
  "Urls": "https://localhost:5021",
  "Kestrel": {
    "Certificates": {
      "Default": {
        "Path": "certificate.pfx",
        "Password": "certificate_password"
      }
    }
  }
}
```

## 🐛 Troubleshooting

### Common Issues

1. **404 on React Routes**
   - Ensure SPA fallback is configured in Program.cs
   - Check that `index.html` exists in `wwwroot`

2. **API Calls Failing**
   - Verify API base URL configuration in `src/config/api.ts`
   - Check that API controllers are properly mapped

3. **Static Files Not Loading**
   - Ensure `UseStaticFiles()` is called before `UseSpa()`
   - Verify files exist in `wwwroot` directory

### Debug Steps

1. **Check build output**:
   ```bash
   ls -la backend/HRMS.API/wwwroot/
   ```

2. **Verify API endpoints**:
   ```bash
   curl http://localhost:5020/api/v1/health
   ```

3. **Check logs**:
   ```bash
   cd backend/HRMS.API
   dotnet run --configuration Release --verbosity detailed
   ```

## 📊 Performance Considerations

### Advantages
- **Reduced latency** - No cross-origin requests
- **Simplified caching** - Single origin for cache policies
- **Fewer connections** - Single server to maintain

### Optimizations
- **Static file caching** - Configure appropriate cache headers
- **Compression** - Enable gzip compression for static assets
- **CDN integration** - Consider CDN for static assets in production

## 🔄 CI/CD Integration

### GitHub Actions Example

```yaml
name: Build and Deploy HRMS
on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Setup .NET
        uses: actions/setup-dotnet@v1
        with:
          dotnet-version: '8.0.x'
      - name: Build Application
        run: ./build-and-deploy.sh
      - name: Deploy to Server
        run: |
          # Your deployment commands here
          scp -r publish/ user@server:/path/to/app/
```

## 📝 Next Steps

1. **Update production domains** in CORS configuration
2. **Configure HTTPS certificates** for production
3. **Set up monitoring** and logging
4. **Configure reverse proxy** (nginx/Apache) if needed
5. **Set up automated backups** for the database

## 🆘 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review the application logs
3. Verify all configuration files are correct
4. Test with the provided scripts first
