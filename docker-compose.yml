version: '3.8'

services:
  hrms-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hrms-application
    ports:
      - "5020:5020"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:5020
      # Database connection (using external SQL Server)
      - ConnectionStrings__DefaultConnection=Server=103.145.50.203,2829;Database=dbHRMS;User Id=userHRMS;Password=***********;TrustServerCertificate=true;Connection Timeout=30;
      # JWT Configuration
      - Jwt__SecretKey=your-super-secret-jwt-key-that-is-at-least-32-characters-long-for-production
      - Jwt__Issuer=HRMS.API
      - Jwt__Audience=HRMS.Client
      - Jwt__ExpirationMinutes=480
      # Database settings
      - Database__SchemaPrefix=org_
      - Database__AutoProvision=true
      - Database__TimeoutSeconds=300
      - Database__ConnectionRetryCount=5
      - Database__ConnectionRetryDelay=10
      # Admin credentials
      - AdminCredentials__GenerateOnStartup=false
      - AdminCredentials__RequirePasswordReset=true
      - AdminCredentials__SuperAdmin__Email=<EMAIL>
      - AdminCredentials__SuperAdmin__Name=System Administrator
      # Security settings
      - Security__EnableMobileBlocking=true
    volumes:
      # Optional: Mount logs directory
      - hrms-logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5020/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - hrms-network

  # Optional: Add a reverse proxy (nginx) for production
  nginx:
    image: nginx:alpine
    container_name: hrms-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # Mount SSL certificates if available
    depends_on:
      - hrms-app
    restart: unless-stopped
    networks:
      - hrms-network
    profiles:
      - production  # Only start nginx with --profile production

volumes:
  hrms-logs:
    driver: local

networks:
  hrms-network:
    driver: bridge
