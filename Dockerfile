# HRMS Standalone Docker Deployment
# Single Dockerfile that builds and runs both React frontend and .NET Core backend on port 5020
# Usage: docker build -t hrms . && docker run -p 5020:5020 hrms

# Stage 1: Build Frontend (Node.js)
FROM node:18-alpine AS frontend-build

WORKDIR /app

# Copy package files for dependency installation
COPY package*.json ./
COPY bun.lockb* ./

# Install frontend dependencies
RUN npm install

# Copy frontend source code
COPY src/ ./src/
COPY public/ ./public/
COPY index.html ./
COPY vite.config.ts ./
COPY tsconfig*.json ./
COPY tailwind.config.ts ./
COPY postcss.config.js ./
COPY components.json ./
COPY eslint.config.js* ./

# Create backend directory structure for build output
RUN mkdir -p backend/HRMS.API

# Override vite config for Docker build - output directly to backend wwwroot
RUN echo 'import { defineConfig } from "vite";\
import react from "@vitejs/plugin-react-swc";\
import path from "path";\
export default defineConfig({\
  plugins: [react()],\
  resolve: { alias: { "@": path.resolve(__dirname, "./src") } },\
  build: {\
    outDir: "./backend/HRMS.API/wwwroot",\
    emptyOutDir: true,\
    assetsDir: "assets"\
  },\
  base: "/"\
});' > vite.config.docker.ts

# Build frontend for production
RUN npm run build -- --config vite.config.docker.ts

# Stage 2: Build Backend (.NET Core)
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS backend-build

WORKDIR /app

# Copy solution and project files first for better caching
COPY backend/HRMS.sln ./
COPY backend/HRMS.API/HRMS.API.csproj ./HRMS.API/
COPY backend/HRMS.Application/HRMS.Application.csproj ./HRMS.Application/
COPY backend/HRMS.Core/HRMS.Core.csproj ./HRMS.Core/
COPY backend/HRMS.Infrastructure/HRMS.Infrastructure.csproj ./HRMS.Infrastructure/

# Restore .NET dependencies
RUN dotnet restore

# Copy all backend source code
COPY backend/ ./

# Copy frontend build output from previous stage
COPY --from=frontend-build /app/backend/HRMS.API/wwwroot/ ./HRMS.API/wwwroot/

# Build and publish the application
RUN dotnet publish HRMS.API/HRMS.API.csproj -c Release -o /app/publish --no-restore

# Stage 3: Production Runtime
FROM mcr.microsoft.com/dotnet/aspnet:8.0

# Install curl for health checks and clean up
RUN apt-get update && \
    apt-get install -y curl && \
    rm -rf /var/lib/apt/lists/* && \
    apt-get clean

WORKDIR /app

# Copy published application
COPY --from=backend-build /app/publish .

# Create non-root user for security
RUN groupadd -r hrmsgroup && \
    useradd -r -g hrmsgroup hrmsuser && \
    chown -R hrmsuser:hrmsgroup /app

# Switch to non-root user
USER hrmsuser

# Expose port 5020
EXPOSE 5020

# Set production environment variables
ENV ASPNETCORE_ENVIRONMENT=Production \
    ASPNETCORE_URLS=http://+:5020 \
    DOTNET_RUNNING_IN_CONTAINER=true \
    DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false \
    ConnectionStrings__DefaultConnection="Server=103.145.50.203,2829;Database=dbHRMS;User Id=userHRMS;Password=***********;TrustServerCertificate=true;Connection Timeout=30;" \
    Jwt__SecretKey="your-super-secret-jwt-key-that-is-at-least-32-characters-long-for-production-docker-deployment" \
    Jwt__Issuer="HRMS.API" \
    Jwt__Audience="HRMS.Client" \
    Jwt__ExpirationMinutes="480" \
    Database__SchemaPrefix="org_" \
    Database__AutoProvision="true" \
    Database__TimeoutSeconds="300" \
    Database__ConnectionRetryCount="5" \
    Database__ConnectionRetryDelay="10" \
    AdminCredentials__GenerateOnStartup="false" \
    AdminCredentials__RequirePasswordReset="true" \
    AdminCredentials__SuperAdmin__Email="<EMAIL>" \
    AdminCredentials__SuperAdmin__Name="System Administrator" \
    Security__EnableMobileBlocking="true" \
    Security__MobileBlockingMessage="This HRMS application is designed for desktop/laptop computers only. Please access from a desktop or laptop computer." \
    Logging__LogLevel__Default="Warning" \
    Logging__LogLevel__Microsoft.AspNetCore="Warning" \
    Logging__LogLevel__Microsoft.EntityFrameworkCore="Error"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5020/api/v1/health || exit 1

# Start the application
ENTRYPOINT ["dotnet", "HRMS.API.dll"]

# Labels for better container management
LABEL maintainer="HRMS Team" \
      description="HRMS Application - React Frontend + .NET Core Backend" \
      version="1.0" \
      port="5020"
