# HRMS Single-Port Docker Deployment
# This Dockerfile builds both React frontend and .NET Core backend in a single container

# Stage 1: Build Frontend (Node.js)
FROM node:18-alpine AS frontend-build

WORKDIR /app/frontend

# Copy package files
COPY package*.json ./
COPY bun.lockb ./

# Install dependencies
RUN npm install

# Copy frontend source code
COPY src/ ./src/
COPY public/ ./public/
COPY index.html ./
COPY vite.config.ts ./
COPY tsconfig*.json ./
COPY tailwind.config.ts ./
COPY postcss.config.js ./
COPY components.json ./
COPY eslint.config.js ./

# Build frontend for production (outputs to backend/HRMS.API/wwwroot)
RUN npm run build:production

# Stage 2: Build Backend (.NET Core)
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS backend-build

WORKDIR /app/backend

# Copy solution and project files
COPY backend/HRMS.sln ./
COPY backend/HRMS.API/HRMS.API.csproj ./HRMS.API/
COPY backend/HRMS.Application/HRMS.Application.csproj ./HRMS.Application/
COPY backend/HRMS.Core/HRMS.Core.csproj ./HRMS.Core/
COPY backend/HRMS.Infrastructure/HRMS.Infrastructure.csproj ./HRMS.Infrastructure/

# Restore dependencies
RUN dotnet restore

# Copy all backend source code
COPY backend/ ./

# Copy frontend build output to backend wwwroot
COPY --from=frontend-build /app/frontend/backend/HRMS.API/wwwroot/ ./HRMS.API/wwwroot/

# Build and publish the application
RUN dotnet publish HRMS.API/HRMS.API.csproj -c Release -o /app/publish --no-restore

# Stage 3: Runtime Image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS runtime

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy published application
COPY --from=backend-build /app/publish .

# Create non-root user for security
RUN addgroup --system --gid 1001 hrmsgroup && \
    adduser --system --uid 1001 --gid 1001 hrmsuser && \
    chown -R hrmsuser:hrmsgroup /app

USER hrmsuser

# Expose port
EXPOSE 5020

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5020/api/v1/health || exit 1

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:5020

# Start the application
ENTRYPOINT ["dotnet", "HRMS.API.dll"]
