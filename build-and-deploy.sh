#!/bin/bash

# HRMS Single-Port Deployment Build Script
# This script builds both the React frontend and .NET backend for single-port deployment

set -e  # Exit on any error

echo "🚀 Starting HRMS Single-Port Deployment Build..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    if ! command -v dotnet &> /dev/null; then
        print_error ".NET Core is not installed. Please install .NET Core first."
        exit 1
    fi
    
    print_success "All dependencies are available."
}

# Clean previous builds
clean_builds() {
    print_status "Cleaning previous builds..."
    
    # Clean frontend dist
    if [ -d "dist" ]; then
        rm -rf dist
        print_status "Removed frontend dist directory"
    fi
    
    # Clean backend wwwroot
    if [ -d "backend/HRMS.API/wwwroot" ]; then
        rm -rf backend/HRMS.API/wwwroot
        print_status "Removed backend wwwroot directory"
    fi
    
    # Clean backend bin/obj
    find backend -name "bin" -type d -exec rm -rf {} + 2>/dev/null || true
    find backend -name "obj" -type d -exec rm -rf {} + 2>/dev/null || true
    
    print_success "Build directories cleaned."
}

# Install frontend dependencies
install_frontend_deps() {
    print_status "Installing frontend dependencies..."
    npm install
    print_success "Frontend dependencies installed."
}

# Build frontend
build_frontend() {
    print_status "Building React frontend for production..."
    npm run build:production
    
    if [ ! -d "backend/HRMS.API/wwwroot" ]; then
        print_error "Frontend build failed - wwwroot directory not created"
        exit 1
    fi
    
    print_success "Frontend built successfully and copied to backend/HRMS.API/wwwroot"
}

# Restore backend dependencies
restore_backend_deps() {
    print_status "Restoring backend dependencies..."
    cd backend
    dotnet restore
    cd ..
    print_success "Backend dependencies restored."
}

# Build backend
build_backend() {
    print_status "Building .NET backend..."
    cd backend
    dotnet build --configuration Release --no-restore
    cd ..
    print_success "Backend built successfully."
}

# Publish backend for deployment
publish_backend() {
    print_status "Publishing backend for deployment..."
    cd backend/HRMS.API
    dotnet publish --configuration Release --output ../../publish --no-build
    cd ../..
    print_success "Backend published to ./publish directory."
}

# Verify build
verify_build() {
    print_status "Verifying build..."
    
    # Check if wwwroot exists and has content
    if [ ! -d "backend/HRMS.API/wwwroot" ] || [ -z "$(ls -A backend/HRMS.API/wwwroot)" ]; then
        print_error "Frontend build verification failed - wwwroot is empty"
        exit 1
    fi
    
    # Check if publish directory exists
    if [ ! -d "publish" ] || [ -z "$(ls -A publish)" ]; then
        print_error "Backend publish verification failed - publish directory is empty"
        exit 1
    fi
    
    # Check if index.html exists
    if [ ! -f "backend/HRMS.API/wwwroot/index.html" ]; then
        print_error "Frontend index.html not found in wwwroot"
        exit 1
    fi
    
    print_success "Build verification completed successfully."
}

# Display build summary
display_summary() {
    echo ""
    echo "🎉 Build completed successfully!"
    echo ""
    echo "📁 Build Output:"
    echo "   • Frontend: backend/HRMS.API/wwwroot/"
    echo "   • Backend: publish/"
    echo ""
    echo "🚀 Deployment Options:"
    echo ""
    echo "1. Development Testing:"
    echo "   cd backend/HRMS.API"
    echo "   dotnet run"
    echo "   Open: http://localhost:5020"
    echo ""
    echo "2. Production Deployment:"
    echo "   Copy the 'publish' directory to your server"
    echo "   Configure IIS or use Kestrel directly"
    echo ""
    echo "📝 Notes:"
    echo "   • Frontend and backend now run on the same port"
    echo "   • API endpoints are available at /api/*"
    echo "   • All other routes serve the React SPA"
    echo "   • Swagger UI is available at /api/swagger (dev only)"
    echo ""
}

# Main execution
main() {
    echo "Starting build process..."
    
    check_dependencies
    clean_builds
    install_frontend_deps
    build_frontend
    restore_backend_deps
    build_backend
    publish_backend
    verify_build
    display_summary
}

# Run main function
main "$@"
