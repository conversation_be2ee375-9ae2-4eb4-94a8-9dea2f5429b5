using System.Text;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using FluentValidation.AspNetCore;
using HRMS.Infrastructure.Data;
using HRMS.Infrastructure.Services;
using HRMS.Infrastructure.Interfaces;
using HRMS.Infrastructure.Repositories;
using HRMS.Application.Services;
using HRMS.Application.Interfaces;
using HRMS.Core.Interfaces;
using HRMS.Core.Services;
using HRMS.API.Middleware;
using HRMS.API.Authentication;
using FluentValidation;
using System.Globalization;

var builder = WebApplication.CreateBuilder(args);

// Configure application to use Indian Standard Time (IST)
TimeZoneInfo.ClearCachedData();
var istTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Asia/Kolkata");
AppContext.SetSwitch("Switch.System.Globalization.UseNls", false);

// Set culture to Indian culture for proper date/time formatting
var culture = new CultureInfo("en-IN");
culture.DateTimeFormat.ShortDatePattern = "dd/MM/yyyy";
culture.DateTimeFormat.LongDatePattern = "dd MMMM yyyy";
culture.DateTimeFormat.ShortTimePattern = "HH:mm";
culture.DateTimeFormat.LongTimePattern = "HH:mm:ss";
CultureInfo.DefaultThreadCurrentCulture = culture;
CultureInfo.DefaultThreadCurrentUICulture = culture;

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddFluentValidationAutoValidation();

// Register validators
builder.Services.AddValidatorsFromAssemblyContaining<HRMS.Application.Validators.LoginRequestValidator>();

// Database configuration with connection string validation
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
if (string.IsNullOrEmpty(connectionString))
{
    throw new InvalidOperationException("Database connection string 'DefaultConnection' is not configured.");
}

// Detect database provider based on connection string
Console.WriteLine($"Connection string: {connectionString}");
bool isSqlite = connectionString.Contains(".db");
bool isSqlServer = connectionString.Contains("Server=") || (connectionString.Contains("Data Source=") && !isSqlite);
Console.WriteLine($"Is SQLite: {isSqlite}, Is SQL Server: {isSqlServer}");

if (isSqlServer)
{
    // Replace placeholders with environment variables if they exist
    connectionString = connectionString
        .Replace("{SQL_SERVER_HOST}", Environment.GetEnvironmentVariable("SQL_SERVER_HOST") ?? "{SQL_SERVER_HOST}")
        .Replace("{SQL_SERVER_DATABASE}", Environment.GetEnvironmentVariable("SQL_SERVER_DATABASE") ?? "{SQL_SERVER_DATABASE}")
        .Replace("{SQL_SERVER_USERNAME}", Environment.GetEnvironmentVariable("SQL_SERVER_USERNAME") ?? "{SQL_SERVER_USERNAME}")
        .Replace("{SQL_SERVER_PASSWORD}", Environment.GetEnvironmentVariable("SQL_SERVER_PASSWORD") ?? "{SQL_SERVER_PASSWORD}");

    // Validate that placeholders have been replaced
    if (connectionString.Contains("{SQL_SERVER_"))
    {
        throw new InvalidOperationException("SQL Server connection string contains unresolved placeholders. Please set the required environment variables: SQL_SERVER_HOST, SQL_SERVER_DATABASE, SQL_SERVER_USERNAME, SQL_SERVER_PASSWORD");
    }

    builder.Services.AddDbContext<HRMSDbContext>(options =>
        options.UseSqlServer(connectionString, sqlOptions =>
        {
            sqlOptions.EnableRetryOnFailure(
                maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(5),
                errorNumbersToAdd: null);
            sqlOptions.CommandTimeout(300); // 5 minutes timeout for long operations
        }));
}
else if (isSqlite)
{
    builder.Services.AddDbContext<HRMSDbContext>(options =>
        options.UseSqlite(connectionString));
}
else
{
    throw new InvalidOperationException("Unsupported database provider. Please configure either SQL Server or SQLite connection string.");
}

// Multi-tenant services
builder.Services.AddScoped<ITenantDbContextFactory, TenantDbContextFactory>();
builder.Services.AddScoped<ITenantService, TenantService>();
builder.Services.AddScoped<IUnitOfWork, TenantUnitOfWork>();
builder.Services.AddScoped<IMasterDatabaseService, MasterDatabaseService>();

// Application services
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IEmployeeService, EmployeeService>();
builder.Services.AddScoped<IEmployeeManagementService, EmployeeManagementService>();
builder.Services.AddScoped<IEmployeeIntegrationService, EmployeeIntegrationService>();
builder.Services.AddScoped<IPermissionService, PermissionService>();
builder.Services.AddScoped<IAttendanceService, AttendanceService>();
builder.Services.AddScoped<ILeaveService, LeaveService>();
builder.Services.AddScoped<IDynamicLeaveBalanceService, DynamicLeaveBalanceService>();
builder.Services.AddScoped<ILeaveBalanceSyncService, LeaveBalanceSyncService>();
builder.Services.AddScoped<ITaskService, TaskService>();
builder.Services.AddScoped<IPerformanceService, PerformanceService>();
builder.Services.AddScoped<IPayrollService, PayrollService>();
builder.Services.AddScoped<IRecruitmentService, RecruitmentService>();
builder.Services.AddScoped<ISuperAdminService, SuperAdminService>();
builder.Services.AddScoped<IOrganizationAdminService, OrganizationAdminService>();
builder.Services.AddScoped<IJwtService, JwtService>();
builder.Services.AddScoped<IDataSeedingService, DataSeedingService>();
builder.Services.AddScoped<IPasswordService, PasswordService>();
builder.Services.AddScoped<IDatabaseProvisioningService, DatabaseProvisioningService>();
builder.Services.AddScoped<HRMS.Core.Interfaces.IDatabaseUpdateService, HRMS.Infrastructure.Services.DatabaseUpdateService>();
builder.Services.AddScoped<ICredentialGenerationService, CredentialGenerationService>();
builder.Services.AddScoped<IPasswordValidationService, PasswordValidationService>();
builder.Services.AddScoped<IAdminCredentialService, AdminCredentialService>();
builder.Services.AddScoped<ISuperAdminCredentialService, SuperAdminCredentialService>();
builder.Services.AddSingleton<ITimeZoneService, TimeZoneService>();

// Rate Limiting Services
builder.Services.AddMemoryCache();
builder.Services.AddSingleton<IRateLimitingService, RateLimitingService>();

// Token Security Services
builder.Services.AddSingleton<ITokenBlacklistService, TokenBlacklistService>();

// Enhanced JWT Authentication with Token Blacklisting
var jwtSettings = builder.Configuration.GetSection("Jwt");
var secretKey = jwtSettings["SecretKey"] ?? throw new InvalidOperationException("JWT SecretKey not configured");

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddScheme<JwtBearerOptions, EnhancedJwtAuthenticationHandler>(JwtBearerDefaults.AuthenticationScheme, options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(secretKey)),
        ValidateIssuer = true,
        ValidIssuer = jwtSettings["Issuer"] ?? "HRMS.API",
        ValidateAudience = true,
        ValidAudience = jwtSettings["Audience"] ?? "HRMS.Client",
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };
});

builder.Services.AddAuthorization();

// Static files and SPA configuration
builder.Services.AddSpaStaticFiles(configuration =>
{
    configuration.RootPath = "wwwroot";
});

// CORS - Enhanced security configuration
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend", policy =>
    {
        // Define allowed origins explicitly
        var allowedOrigins = new[]
        {
            "http://localhost:5173", // Vite dev server
            "http://localhost:3000", // React dev server
            "http://localhost:8080", // Alternative dev server
            "http://localhost:8081", // Current frontend
            "https://localhost:5173", // HTTPS versions
            "https://localhost:3000",
            "https://localhost:8080",
            "https://localhost:8081"
        };

        policy.WithOrigins(allowedOrigins)
              .WithMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH") // Explicit methods
              .WithHeaders("Content-Type", "Authorization", "X-Organization-ID", "X-Requested-With") // Explicit headers
              .AllowCredentials()
              .SetPreflightMaxAge(TimeSpan.FromMinutes(10)) // Cache preflight for 10 minutes
              .WithExposedHeaders("X-RateLimit-Limit", "X-RateLimit-Remaining", "X-RateLimit-Reset"); // Expose rate limit headers
    });

    // Add a more restrictive policy for production
    options.AddPolicy("Production", policy =>
    {
        policy.WithOrigins("https://hrms.yourdomain.com") // Replace with actual production domain
              .WithMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH")
              .WithHeaders("Content-Type", "Authorization", "X-Organization-ID")
              .AllowCredentials()
              .SetPreflightMaxAge(TimeSpan.FromHours(1));
    });
});

// Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "HRMS API",
        Version = "v1.0.0",
        Description = @"
# Human Resource Management System API

A comprehensive HRMS backend API built with .NET Core 8.0, featuring:

## 🔐 Authentication & Authorization
- JWT-based authentication
- Role-based access control (SuperAdmin, OrgAdmin, Employee)
- Multi-tenant architecture with schema isolation

## 📊 Core Modules
- **User Management**: Employee profiles, roles, and permissions
- **Attendance Management**: Check-in/out, attendance tracking
- **Leave Management**: Leave applications, approvals, balance tracking
- **Task Management**: Task assignment, progress tracking, comments
- **Performance Management**: Reviews, goals, ratings
- **Payroll Management**: Salary information, benefits
- **Recruitment Management**: Job postings, applications, pipeline

## 🏢 Admin Features
- **Super Admin**: System-wide analytics, organization management
- **Organization Admin**: Dashboard, billing, organization metrics

## 🛡️ Security Features
- Password hashing with BCrypt
- Input validation with FluentValidation
- Comprehensive error handling
- Security headers middleware

## 🏗️ Architecture
- Clean Architecture with 4 layers
- Repository pattern with Unit of Work
- Multi-tenant database with SQL Server
- Automatic schema provisioning
",
        Contact = new OpenApiContact
        {
            Name = "HRMS API Support",
            Email = "<EMAIL>"
        },
        License = new OpenApiLicense
        {
            Name = "MIT License"
        }
    });

    // Add JWT authentication to Swagger
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = @"JWT Authorization header using the Bearer scheme.
                      Enter 'Bearer' [space] and then your token in the text input below.
                      Example: 'Bearer 12345abcdef'",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // Group endpoints by tags
    c.TagActionsBy(api => new[] { api.GroupName ?? api.ActionDescriptor.RouteValues["controller"] });
    c.DocInclusionPredicate((name, api) => true);
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "HRMS API V1");
        c.RoutePrefix = "api/swagger"; // Move Swagger to /api/swagger to avoid conflicts
    });
}

app.UseHttpsRedirection();

// Static files middleware - serve React build files
app.UseStaticFiles();
app.UseSpaStaticFiles();

// Custom middleware
app.UseMiddleware<SecurityHeadersMiddleware>();
app.UseMiddleware<GlobalExceptionMiddleware>();
app.UseMiddleware<MobileDeviceBlockingMiddleware>(); // Add mobile device blocking early in pipeline
app.UseMiddleware<RateLimitingMiddleware>();
app.UseMiddleware<ValidationMiddleware>();
app.UseMiddleware<RequestLoggingMiddleware>();

app.UseCors("AllowFrontend");

app.UseAuthentication();
app.UseMiddleware<TenantMiddleware>();
app.UseAuthorization();

// API routes
app.MapControllers();

// SPA fallback - serve React app for all non-API routes
app.UseSpa(spa =>
{
    spa.Options.SourcePath = "wwwroot";

    if (app.Environment.IsDevelopment())
    {
        // In development, you can still use the Vite dev server if needed
        // spa.UseProxyToSpaDevelopmentServer("http://localhost:8080");

        // Or serve the built files directly
        spa.Options.DefaultPage = "/index.html";
    }
    else
    {
        // In production, serve the built React files
        spa.Options.DefaultPage = "/index.html";
    }
});

// Initialize database on startup
await InitializeDatabaseAsync(app);

app.Run();

// Database initialization method
static async Task InitializeDatabaseAsync(WebApplication app)
{
    using var scope = app.Services.CreateScope();
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

    try
    {
        logger.LogInformation("Initializing database...");

        // Get the master database context to ensure master database exists
        var contextFactory = scope.ServiceProvider.GetRequiredService<ITenantDbContextFactory>();
        using var masterContext = contextFactory.CreateMasterDbContext();

        // Check if database exists and handle accordingly
        var canConnect = await masterContext.Database.CanConnectAsync();
        if (!canConnect)
        {
            logger.LogInformation("Creating master database...");
            await masterContext.Database.MigrateAsync();
            logger.LogInformation("Master database created successfully");
        }
        else
        {
            // Run any pending migrations on the master schema
            var pendingMigrations = await masterContext.Database.GetPendingMigrationsAsync();
            if (pendingMigrations.Any())
            {
                logger.LogInformation("Applying {Count} pending migrations to master database", pendingMigrations.Count());
                await masterContext.Database.MigrateAsync();
                logger.LogInformation("Master database migrations applied successfully");
            }
            else
            {
                logger.LogInformation("Master database is up to date");
            }
        }

        logger.LogInformation("Database initialization completed successfully");

        // Seed initial data
        var dataSeedingService = scope.ServiceProvider.GetRequiredService<IDataSeedingService>();
        await dataSeedingService.SeedInitialDataAsync();
        logger.LogInformation("Data seeding completed successfully");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Failed to initialize database. Please check your SQL Server connection and credentials.");
        throw;
    }
}
