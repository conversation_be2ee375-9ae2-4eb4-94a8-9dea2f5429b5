# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Build outputs
dist/
build/
publish/

# .NET Core
backend/**/bin/
backend/**/obj/
backend/**/*.user
backend/**/*.suo
backend/**/*.userosscache
backend/**/*.sln.docstates
backend/**/project.lock.json
backend/**/project.fragment.lock.json
backend/**/artifacts/
backend/**/.vs/

# Database files
*.db
*.db-shm
*.db-wal

# Logs
logs/
*.log

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
*.md
!README.md

# Scripts
*.sh

# Test files
test_*.js
test_*.cjs
*test*.html
check_*.js
create_*.js
debug_*.js
delete_*.js
fix_*.js
manual_*.js
reset_*.js
run_*.js
update_*.js
verify_*.js
cleanup_*.js
initialize_*.js
execute_*.sh

# SQL files
*.sql

# Config files that shouldn't be in container
config/
migrations/
seeds/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Package lock files (we copy them explicitly)
# package-lock.json
# bun.lockb
