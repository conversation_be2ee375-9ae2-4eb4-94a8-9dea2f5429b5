# HRMS Docker Deployment Guide

This guide explains how to deploy your HRMS system using Docker containers with both frontend and backend in a single container.

## 🐳 Docker Architecture

```
┌─────────────────────────────────────────┐
│           Docker Container              │
├─────────────────────────────────────────┤
│  .NET Core Runtime + React Frontend    │
├─────────────────────────────────────────┤
│  ├─ /api/*     → API Endpoints         │
│  ├─ /api/swagger → Swagger UI (dev)    │
│  └─ /*         → React SPA             │
└─────────────────────────────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Docker installed
- Docker Compose installed

### 1. Development Deployment
```bash
# Start development environment
./docker-deploy.sh dev

# Access the application
# Main App: http://localhost:5020
# API Docs: http://localhost:5020/api/swagger
```

### 2. Production Deployment
```bash
# Start production environment (with nginx)
./docker-deploy.sh prod

# Access the application
# Main App: http://localhost:80 (nginx) or http://localhost:5020 (direct)
```

## 📋 Available Commands

### Build Commands
```bash
# Build Docker image
./docker-deploy.sh build

# Build without cache
./docker-deploy.sh build --no-cache
```

### Deployment Commands
```bash
# Development environment
./docker-deploy.sh dev
./docker-deploy.sh dev --rebuild

# Production environment
./docker-deploy.sh prod
./docker-deploy.sh prod --rebuild
```

### Management Commands
```bash
# View logs
./docker-deploy.sh logs

# Check health
./docker-deploy.sh health

# Stop containers
./docker-deploy.sh stop

# Clean up everything
./docker-deploy.sh clean
```

## 🏗️ Multi-Stage Build Process

The Dockerfile uses a 3-stage build:

### Stage 1: Frontend Build (Node.js)
- Uses `node:18-alpine` image
- Installs npm dependencies
- Builds React application
- Outputs to `backend/HRMS.API/wwwroot`

### Stage 2: Backend Build (.NET Core)
- Uses `mcr.microsoft.com/dotnet/sdk:8.0` image
- Restores .NET dependencies
- Copies frontend build from Stage 1
- Builds and publishes .NET application

### Stage 3: Runtime (Production)
- Uses `mcr.microsoft.com/dotnet/aspnet:8.0` image
- Copies published application
- Sets up non-root user for security
- Configures health checks

## 🔧 Configuration

### Environment Variables

The application supports configuration via environment variables:

```bash
# Database
ConnectionStrings__DefaultConnection=Server=...

# JWT
Jwt__SecretKey=your-secret-key
Jwt__Issuer=HRMS.API
Jwt__Audience=HRMS.Client
Jwt__ExpirationMinutes=480

# Database Settings
Database__SchemaPrefix=org_
Database__AutoProvision=true

# Admin Credentials
AdminCredentials__GenerateOnStartup=false
AdminCredentials__SuperAdmin__Email=<EMAIL>

# Security
Security__EnableMobileBlocking=true
```

### Docker Compose Files

#### `docker-compose.yml` (Production)
- Production-ready configuration
- Environment variables for production
- Optional nginx reverse proxy
- Health checks enabled

#### `docker-compose.dev.yml` (Development)
- Development configuration
- Debug-friendly settings
- Auto-generated admin credentials
- Detailed logging

## 🔒 Security Features

### Container Security
- **Non-root user**: Application runs as `hrmsuser` (UID 1001)
- **Minimal base image**: Uses official Microsoft ASP.NET runtime
- **Health checks**: Built-in health monitoring
- **Security headers**: Configured via nginx

### Network Security
- **Rate limiting**: API and login endpoints protected
- **CORS configuration**: Environment-specific origins
- **SSL/TLS ready**: nginx configuration for HTTPS

## 🌐 Production Deployment Options

### Option 1: Direct Container
```bash
# Build and run directly
docker build -t hrms-app .
docker run -p 5020:5020 \
  -e ConnectionStrings__DefaultConnection="Server=..." \
  hrms-app
```

### Option 2: Docker Compose (Recommended)
```bash
# Production with nginx
./docker-deploy.sh prod

# Or manually
docker-compose up -d
```

### Option 3: Docker Swarm
```bash
# Deploy to swarm
docker stack deploy -c docker-compose.yml hrms-stack
```

### Option 4: Kubernetes
```yaml
# Example Kubernetes deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hrms-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: hrms-app
  template:
    metadata:
      labels:
        app: hrms-app
    spec:
      containers:
      - name: hrms-app
        image: hrms-app:latest
        ports:
        - containerPort: 5020
        env:
        - name: ConnectionStrings__DefaultConnection
          value: "Server=..."
```

## 📊 Monitoring & Logging

### Health Checks
```bash
# Check container health
./docker-deploy.sh health

# Manual health check
curl http://localhost:5020/api/v1/health
```

### Logs
```bash
# View application logs
./docker-deploy.sh logs

# View specific container logs
docker logs hrms-application -f
```

### Metrics
The application exposes health endpoints for monitoring:
- `/api/v1/health` - Basic health check
- Container health checks every 30 seconds

## 🔧 Troubleshooting

### Common Issues

#### 1. Build Failures
```bash
# Clean build without cache
./docker-deploy.sh build --no-cache

# Check Docker daemon
docker info
```

#### 2. Container Won't Start
```bash
# Check logs
./docker-deploy.sh logs

# Check container status
docker ps -a
```

#### 3. Database Connection Issues
```bash
# Verify environment variables
docker exec hrms-application env | grep Connection

# Test database connectivity
docker exec hrms-application curl -f http://localhost:5020/api/v1/health
```

#### 4. Frontend Not Loading
```bash
# Check if wwwroot exists in container
docker exec hrms-application ls -la /app/wwwroot/

# Verify build output
docker exec hrms-application cat /app/wwwroot/index.html
```

### Debug Commands
```bash
# Enter container shell
docker exec -it hrms-application /bin/bash

# Check running processes
docker exec hrms-application ps aux

# Check disk usage
docker exec hrms-application df -h
```

## 🚀 CI/CD Integration

### GitHub Actions Example
```yaml
name: Build and Deploy HRMS Docker
on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Build Docker image
        run: docker build -t hrms-app:${{ github.sha }} .
      
      - name: Push to registry
        run: |
          docker tag hrms-app:${{ github.sha }} your-registry/hrms-app:latest
          docker push your-registry/hrms-app:latest
      
      - name: Deploy to production
        run: |
          # Your deployment commands
          ssh user@server "docker pull your-registry/hrms-app:latest && docker-compose up -d"
```

## 📈 Performance Optimization

### Image Size Optimization
- Multi-stage build reduces final image size
- `.dockerignore` excludes unnecessary files
- Alpine-based Node.js image for smaller footprint

### Runtime Optimization
- Health checks for automatic recovery
- Resource limits in production
- Nginx caching for static assets

### Scaling
```bash
# Scale with Docker Compose
docker-compose up -d --scale hrms-app=3

# Use load balancer (nginx) for multiple instances
```

## 🆘 Support

For issues with Docker deployment:
1. Check the troubleshooting section
2. Review container logs: `./docker-deploy.sh logs`
3. Verify health status: `./docker-deploy.sh health`
4. Test with development environment first: `./docker-deploy.sh dev`
