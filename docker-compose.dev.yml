version: '3.8'

services:
  hrms-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime  # Use the final runtime stage
    container_name: hrms-dev
    ports:
      - "5020:5020"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:5020
      # Database connection
      - ConnectionStrings__DefaultConnection=Server=103.145.50.203,2829;Database=dbHRMS;User Id=userHRMS;Password=***********;TrustServerCertificate=true;Connection Timeout=30;
      # JWT Configuration (development)
      - Jwt__SecretKey=your-super-secret-jwt-key-that-is-at-least-32-characters-long
      - Jwt__Issuer=HRMS.API
      - Jwt__Audience=HRMS.Client
      - Jwt__ExpirationMinutes=1440
      # Database settings
      - Database__SchemaPrefix=org_
      - Database__AutoProvision=true
      - Database__TimeoutSeconds=300
      - Database__ConnectionRetryCount=3
      - Database__ConnectionRetryDelay=5
      # Admin credentials (development)
      - AdminCredentials__GenerateOnStartup=true
      - AdminCredentials__RequirePasswordReset=false
      - AdminCredentials__SuperAdmin__Email=<EMAIL>
      - AdminCredentials__SuperAdmin__Name=System Administrator
      - AdminCredentials__SuperAdmin__Password=Admin123!
      - AdminCredentials__DefaultOrganization__Name=Plan Square
      - AdminCredentials__DefaultOrganization__Domain=plan2intl.com
      - AdminCredentials__DefaultOrganization__Industry=Technology
      - AdminCredentials__DefaultOrganization__AdminEmail=<EMAIL>
      - AdminCredentials__DefaultOrganization__AdminName=Akash Jadhav
      - <EMAIL>=Admin123!
      # Security settings
      - Security__EnableMobileBlocking=true
    volumes:
      # Mount logs for development debugging
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - hrms-dev-network

volumes:
  hrms-dev-logs:
    driver: local

networks:
  hrms-dev-network:
    driver: bridge
