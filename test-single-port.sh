#!/bin/bash

# HRMS Single-Port Testing Script
# This script builds and runs the application in single-port mode for testing

set -e

echo "🧪 Testing HRMS Single-Port Deployment..."

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Build the application
print_status "Building application for single-port deployment..."
./build-and-deploy.sh

# Start the backend server
print_status "Starting HRMS backend server..."
print_warning "The server will serve both API and frontend on the same port"

echo ""
echo "🌐 Application will be available at:"
echo "   • Main App: http://localhost:5020"
echo "   • API Docs: http://localhost:5020/api/swagger"
echo ""
echo "🔑 Test Credentials:"
echo "   • Super Admin: <EMAIL> / Admin123!"
echo "   • Org Admin: <EMAIL> / Admin123!"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

cd backend/HRMS.API
dotnet run --configuration Release
